import React, { useMemo } from 'react';
import {
  DownloadOutlined,
  EyeOutlined,
  UserOutlined,
  TruckOutlined,
  DollarOutlined,
  PayCircleOutlined,
  BorderlessTableOutlined,
  FieldTimeOutlined,
  DeleteOutlined,
  EditOutlined,
  EnvironmentOutlined,
} from '@ant-design/icons';
import { Avatar, Card, List, Skeleton, Space } from 'antd';
import { IMG_URL } from '../configs/app-global';
import numberToPrice from '../helpers/numberToPrice';
import moment from 'moment';
import useDemo from '../helpers/useDemo';
import { useTranslation } from 'react-i18next';

const { Meta } = Card;

const OrderCardSeller = ({
  data: item,
  goToShow,
  loading,
  setLocationsMap,
  setId,
  setIsModalVisible,
  setText,
  setDowloadModal,
  setType,
  orderType,
  setIsTransactionModalOpen,
}) => {
  const { isDemo, demoFunc } = useDemo();
  const { t } = useTranslation();
  const lastTransaction = item.transaction || {};

  // Memoize data array to prevent recreation on every render
  // Note: setIsTransactionModalOpen is a stable React setter function and doesn't need to be in dependencies
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const data = useMemo(() => [
    {
      title: t('client'),
      icon: <UserOutlined />,
      data: item?.user
        ? `${item.user?.firstname || '-'} ${item.user?.lastname || '-'}`
        : t('deleted.user'),
    },
    {
      title: orderType ? t('table') : t('deliveryman'),
      icon: <TruckOutlined />,
      data: orderType
        ? `${item?.table?.name || '-'}`
        : `${item.deliveryman?.firstname || '-'} ${
            item.deliveryman?.lastname || '-'
          }`,
    },
    {
      title: t('amount'),
      icon: <DollarOutlined />,
      data: numberToPrice(
        item.total_price,
        item.currency?.symbol,
        item.currency?.position,
      ),
    },
    {
      title: t('last.payment.type'),
      icon: <PayCircleOutlined />,
      data: lastTransaction?.payment_system?.tag ? t(lastTransaction.payment_system.tag) : '-',
    },
    {
      title: t('payment.status'),
      icon: <BorderlessTableOutlined />,
      data: lastTransaction?.status ? (
        <div
          style={{ cursor: 'pointer' }}
          onClick={(e) => {
            e.stopPropagation();
            setIsTransactionModalOpen(lastTransaction);
          }}
        >
          {t(lastTransaction?.status)}{' '}
          <EditOutlined disabled={item?.deleted_at} />
        </div>
      ) : (
        '-'
      ),
    },
    {
      title: t('delivery.date'),
      icon: <FieldTimeOutlined />,
      data: moment(item?.delivery_date).format('DD MMM YYYY') || '-',
    },
    {
      title: t('created_at'),
      icon: <FieldTimeOutlined />,
      data: moment(item?.created_at).format('DD MMM YYYY') || '-',
    },
  ], [item, lastTransaction, orderType, t]); // Dependencies for memoization

  return (
    <Card
      actions={[
        <EnvironmentOutlined
          onClick={(e) => {
            e.stopPropagation();
            setLocationsMap(item.id);
          }}
        />,
        <EyeOutlined key='setting' onClick={() => goToShow(item)} />,
        <DeleteOutlined
          onClick={(e) => {
            if (isDemo) {
              demoFunc();
              return;
            }
            e.stopPropagation();
            setId([item.id]);
            setIsModalVisible(true);
            setText(true);
            setType(item.status);
          }}
        />,
        <DownloadOutlined
          key='ellipsis'
          onClick={() => setDowloadModal(item.id)}
        />,
      ]}
      className='order-card'
    >
      <Skeleton loading={loading} avatar active>
        <Meta
          avatar={
            <Avatar src={IMG_URL + item.user?.img} icon={<UserOutlined />} />
          }
          description={`#${item.id}`}
          title={`${item.user?.firstname || '-'} ${item.user?.lastname || '-'}`}
        />
        <List
          itemLayout='horizontal'
          dataSource={data}
          renderItem={(item, key) => (
            <List.Item key={key}>
              <Space>
                {item?.icon}
                <span>
                  {`${item?.title}:`}
                  {item?.data}
                </span>
              </Space>
            </List.Item>
          )}
        />
      </Skeleton>
    </Card>
  );
};

export default OrderCardSeller;
