-- Script para adicionar traduções dos status de pagamento
-- Adiciona traduções para os status de transação em português, inglês e espanhol

-- Verificar se as chaves já existem
SET @payment_status_keys = 'progress,paid,canceled,rejected,refund';

-- Verificar quantas chaves já existem
SELECT 
    COUNT(*) as existing_keys,
    GROUP_CONCAT(DISTINCT t.key SEPARATOR ', ') as existing_key_list
FROM translations t 
WHERE FIND_IN_SET(t.key, @payment_status_keys) > 0 
AND t.locale = 'pt-BR'
AND t.group = 'web';

-- Inserir traduções em português (pt-BR) apenas se não existirem
INSERT IGNORE INTO translations (locale, `group`, `key`, value, created_at, updated_at) VALUES
('pt-BR', 'web', 'progress', 'Em Progresso', NOW(), NOW()),
('pt-BR', 'web', 'paid', 'Pago', NOW(), NOW()),
('pt-BR', 'web', 'canceled', 'Cancelado', NOW(), NOW()),
('pt-BR', 'web', 'rejected', 'Rejeitado', NOW(), NOW()),
('pt-BR', 'web', 'refund', 'Reembolso', NOW(), NOW());

-- Inserir traduções em inglês (en) apenas se não existirem
INSERT IGNORE INTO translations (locale, `group`, `key`, value, created_at, updated_at) VALUES
('en', 'web', 'progress', 'In Progress', NOW(), NOW()),
('en', 'web', 'paid', 'Paid', NOW(), NOW()),
('en', 'web', 'canceled', 'Canceled', NOW(), NOW()),
('en', 'web', 'rejected', 'Rejected', NOW(), NOW()),
('en', 'web', 'refund', 'Refund', NOW(), NOW());

-- Inserir traduções em espanhol (es) apenas se não existirem
INSERT IGNORE INTO translations (locale, `group`, `key`, value, created_at, updated_at) VALUES
('es', 'web', 'progress', 'En Progreso', NOW(), NOW()),
('es', 'web', 'paid', 'Pagado', NOW(), NOW()),
('es', 'web', 'canceled', 'Cancelado', NOW(), NOW()),
('es', 'web', 'rejected', 'Rechazado', NOW(), NOW()),
('es', 'web', 'refund', 'Reembolso', NOW(), NOW());

-- Verificar quantas traduções foram inseridas
SELECT 
    COUNT(*) as total_translations_added,
    GROUP_CONCAT(DISTINCT CONCAT(t.locale, ':', t.key, '=', t.value) SEPARATOR ', ') as added_translations
FROM translations t 
WHERE FIND_IN_SET(t.key, @payment_status_keys) > 0 
AND t.locale IN ('pt-BR', 'en', 'es')
AND t.group = 'web';

-- Verificar se todas as traduções estão presentes
SELECT 
    t.locale,
    t.key,
    t.value,
    t.created_at
FROM translations t 
WHERE FIND_IN_SET(t.key, @payment_status_keys) > 0 
AND t.locale IN ('pt-BR', 'en', 'es')
AND t.group = 'web'
ORDER BY t.locale, t.key;
