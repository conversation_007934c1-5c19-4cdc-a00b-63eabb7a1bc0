@echo off
echo ========================================
echo Setup GitHub Repository for Admin Panel
echo ========================================
echo.

REM Solicitar o nome do repositório
set /p REPO_NAME="Digite o nome do repositório no GitHub (ex: admin-ticketflow-chat): "

REM Configurar remote
echo Configurando remote origin...
git remote add origin https://github.com/wellintj/%REPO_NAME%.git

REM Fazer push
echo Fazendo push para o GitHub...
git push -u origin main

echo.
echo ========================================
echo Processo concluído!
echo Seu repositório está disponível em:
echo https://github.com/wellintj/%REPO_NAME%
echo ========================================
pause
